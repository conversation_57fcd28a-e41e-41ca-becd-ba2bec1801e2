<?php

namespace mobileapi\controllers\customer;

use mobileapi\services\customer\ExistingDataService;
use auth\controllers\customer\ExistingDataController as AuthExistingDataController;

/**
 * 存量客资管理-移动端Api控制器
 */
class ExistingDataController extends AuthExistingDataController
{
    use \mobileapi\traits\ControllerTraits;

    /**
     * @var ExistingDataService
     */
    public $serviceClass = ExistingDataService::class;
}
