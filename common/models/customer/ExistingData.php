<?php

namespace common\models\customer;

use common\helpers\DateHelper;
use common\enums\ScenariosEnum;
use services\UserService;
use Yii;

/**
 * This is the model class for table "{{%customer_existing_data}}".
 *
 * @property int $id ID
 * @property int $cus_id 客户ID，customer表ID
 * @property string $mobile 手机号，customer表mobile
 * @property int $store_id 客户归属门店ID，store表ID
 * @property int $phase 阶段，0-待回访 1-已回访
 * @property int $allocation_user 分配人员ID，backend_member表ID
 * @property int $allocation_time 分配时间
 * @property int $last_revisit_time 最近回访时间
 * @property int $entity_id 企业ID
 * @property int $created_at 创建时间
 * @property int $updated_by 更新人
 * @property int $updated_at 更新时间
 */
class ExistingData extends \common\models\Base
{
    /**
     * 需要显示的字段
     */
    public static $showAttrs = [];

    /**
     * 需要隐藏的字段
     */
    public static $hiddenAttrs = [];

    /**
     * 需要扩展的字段
     */
    public static $extendAttrs = [];

    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return '{{%customer_existing_data}}';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['cus_id'], 'integer', 'min' => 0],
            [['mobile'], 'string', 'max' => 20],
            [['store_id'], 'integer', 'min' => 0],
            [['phase'], 'integer', 'min' => 0],
            [['allocation_user'], 'integer', 'min' => 0],
            [['allocation_time'], 'integer', 'min' => 0],
            [['last_revisit_time'], 'integer', 'min' => 0],
            [['cus_id', 'mobile', 'store_id', 'phase', 'allocation_user', 'allocation_time', 'last_revisit_time'], 'required'],
            [['cus_id', 'store_id', 'phase', 'allocation_user', 'allocation_time', 'last_revisit_time'], 'integer'],
            [['mobile'], 'trim'],

        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'cus_id' => '客户ID，customer表ID',
            'mobile' => '手机号，customer表mobile',
            'store_id' => '客户归属门店ID，store表ID',
            'phase' => '阶段，0-待回访 1-已回访',
            'allocation_user' => '分配人员ID，backend_member表ID',
            'allocation_time' => '分配时间',
            'last_revisit_time' => '最近回访时间',
            'entity_id' => '企业ID',
            'created_at' => '创建时间',
            'updated_by' => '更新人',
            'updated_at' => '更新时间',
        ];
    }

    public function scenarios()
    {
        return [
            ScenariosEnum::DEFAULT => ['cus_id', 'mobile', 'store_id', 'phase', 'allocation_user', 'allocation_time', 'last_revisit_time'],
        ];
    }

    public static function find()
    {
        $query = parent::find();
        if (UserService::getInst()->id) {
            $query->andFilterWhere(['entity_id' => UserService::getInst()->current_entity_id]);
        }
        return $query;
    }

    /**
     * 保存数据之前记录操作信息
     * @param bool $isInsert
     * @return bool
     */
    public function beforeSave($isInsert)
    {
        if ($isInsert) {
            $this->created_at = time();
            $this->entity_id = UserService::getInst()->current_entity_id;
        }
        $this->updated_by = UserService::getInst()->id;
        $this->updated_at = time();

        return parent::beforeSave($isInsert);
    }

    public function getCreatedAtText()
    {
        return DateHelper::toDate($this->created_at, 'Y-m-d H:i:s');
    }

    public function getUpdatedAtText()
    {
        return DateHelper::toDate($this->updated_at, 'Y-m-d H:i:s');
    }

    public function getUpdatedByText()
    {
        return $this->updatedPerson->username ?: '';
    }

    public function getUpdatedPerson()
    {
        return $this->hasOne(\common\models\backend\Member::class, ['id' => 'updated_by']);
    }
}
