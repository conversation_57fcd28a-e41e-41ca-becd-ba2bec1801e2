<?php

namespace common\models\customer;

use common\helpers\DateHelper;
use common\enums\ScenariosEnum;
use services\UserService;
use Yii;

/**
 * This is the model class for table "{{%customer_existing_data_log}}".
 *
 * @property int $id ID
 * @property int $customer_existing_data_id customer_existing_data表ID
 * @property string $content 日志内容
 * @property int $created_by 创建人
 * @property int $created_at 创建时间
 */
class ExistingDataLog extends \common\models\Base
{
    /**
     * 需要显示的字段
     */
    public static $showAttrs = [];

    /**
     * 需要隐藏的字段
     */
    public static $hiddenAttrs = [];

    /**
     * 需要扩展的字段
     */
    public static $extendAttrs = [];

    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return '{{%customer_existing_data_log}}';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['customer_existing_data_id'], 'integer', 'min' => 0],
            [['customer_existing_data_id', 'content'], 'required'],
            [['customer_existing_data_id'], 'integer'],
            [['content'], 'trim'],
            [['content'], 'string'],

        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'customer_existing_data_id' => 'customer_existing_data表ID',
            'content' => '日志内容',
            'created_by' => '创建人',
            'created_at' => '创建时间',
        ];
    }

    public function scenarios()
    {
        return [
            ScenariosEnum::DEFAULT => ['customer_existing_data_id', 'content'],
        ];
    }

    public static function find()
    {
        $query = parent::find();
        if (UserService::getInst()->id) {
        }
        return $query;
    }

    /**
     * 保存数据之前记录操作信息
     * @param bool $isInsert
     * @return bool
     */
    public function beforeSave($isInsert)
    {
        if ($isInsert) {
            $this->created_by = UserService::getInst()->id;
            $this->created_at = time();
        }

        return parent::beforeSave($isInsert);
    }

    public function getCreatedAtText()
    {
        return DateHelper::toDate($this->created_at, 'Y-m-d H:i:s');
    }

    public function getCreatedPerson()
    {
        return $this->hasOne(\common\models\backend\Member::class, ['id' => 'created_by']);
    }

    public function getCreatedByText()
    {
        return $this->createdPerson->username ?: '';
    }
}
