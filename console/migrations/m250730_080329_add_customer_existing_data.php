<?php

use common\components\migrate\Migration;

/**
 * Class m250730_080329_add_customer_existing_data
 */
class m250730_080329_add_customer_existing_data extends Migration
{
    /**
     * {@inheritdoc}
     */
    public function safeUp()
    {
        $sql = "
            CREATE TABLE `erp_customer_existing_data` (
                `id` INT(11) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'ID',
                `cus_id` INT(11) UNSIGNED NOT NULL DEFAULT 0 COMMENT '客户ID，customer表ID',
                `mobile` VARCHAR(20) NOT NULL DEFAULT '' COMMENT '手机号，customer表mobile' COLLATE 'utf8_general_ci',
                `store_id` INT(11) UNSIGNED NOT NULL DEFAULT 0 COMMENT '客户归属门店ID，store表ID',
                `phase` TINYINT(4) UNSIGNED NOT NULL DEFAULT 0 COMMENT '阶段，0-待回访 1-已回访',
                `allocation_user` INT(11) UNSIGNED NOT NULL DEFAULT 0 COMMENT '分配人员ID，backend_member表ID',
                `allocation_time` INT(11) UNSIGNED NOT NULL DEFAULT 0 COMMENT '分配时间',
                `last_revisit_time` INT(11) UNSIGNED NOT NULL DEFAULT 0 COMMENT '最近回访时间',
                `entity_id` INT(11) UNSIGNED NOT NULL DEFAULT 0 COMMENT '企业ID',
                `created_at` INT(11) UNSIGNED NOT NULL DEFAULT 0 COMMENT '创建时间',
                `updated_by` INT(11) UNSIGNED NOT NULL DEFAULT 0 COMMENT '更新人',
                `updated_at` INT(11) UNSIGNED NOT NULL DEFAULT 0 COMMENT '更新时间',
                PRIMARY KEY (`id`),
                UNIQUE INDEX `mobile` (`mobile`)
            )
            COMMENT='存量客资表'
            COLLATE='utf8_general_ci'
            ;       
        ";
        $this->execute($sql);
    }

    /**
     * {@inheritdoc}
     */
    public function safeDown()
    {
        echo "m250730_080329_add_customer_existing_data cannot be reverted.\n";

        return false;
    }

    /*
    // Use up()/down() to run migration code without a transaction.
    public function up()
    {

    }

    public function down()
    {
        echo "m250730_080329_add_customer_existing_data cannot be reverted.\n";

        return false;
    }
    */
}
