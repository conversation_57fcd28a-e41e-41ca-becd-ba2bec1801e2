<?php

use common\components\migrate\Migration;

/**
 * Class m250730_080321_add_customer_existing_data_log
 */
class m250730_080321_add_customer_existing_data_log extends Migration
{
    /**
     * {@inheritdoc}
     */
    public function safeUp()
    {
        $sql = "
            CREATE TABLE `erp_customer_existing_data_log` (
                `id` INT(11) UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'ID',
                `customer_existing_data_id` INT(11) UNSIGNED NOT NULL DEFAULT 0 COMMENT 'customer_existing_data表ID',
                `content` TEXT NOT NULL COMMENT '日志内容' COLLATE 'utf8_general_ci',
                `created_by` INT(11) NOT NULL DEFAULT 0 COMMENT '创建人',
                `created_at` INT(11) NOT NULL DEFAULT 0 COMMENT '创建时间',
                PRIMARY KEY (`id`),
                INDEX `customer_existing_data_id` (`customer_existing_data_id`)
            )
            COMMENT='存量客资日志表'
            COLLATE='utf8_general_ci'
            ;       
        ";
        $this->execute($sql);
    }

    /**
     * {@inheritdoc}
     */
    public function safeDown()
    {
        echo "m250730_080321_add_customer_existing_data_log cannot be reverted.\n";

        return false;
    }

    /*
    // Use up()/down() to run migration code without a transaction.
    public function up()
    {

    }

    public function down()
    {
        echo "m250730_080321_add_customer_existing_data_log cannot be reverted.\n";

        return false;
    }
    */
}
